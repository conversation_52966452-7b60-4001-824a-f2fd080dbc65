# 普通话训练功能重构需求

## 背景

当前普通话训练功能存在以下问题：
1. **内容重复率高**：每次点击汉字都实时调用 AI API 生成内容，导致生成的词语、成语和句子经常重复
2. **API 调用成本高**：频繁的 API 调用增加了系统运营成本
3. **用户体验不佳**：每次生成都需要等待 2-3 秒，影响学习流畅性
4. **内容管理缺失**：无法对生成的内容进行编辑、删除等管理操作

为了解决上述问题，需要重构普通话训练功能，采用内容池策略，将功能拆分为训练页和编辑页，实现内容的批量生成和精细化管理。

## 需求

### 功能需求

#### 1. 页面架构重构
- **训练页改造**：基于现有 `src/pages/speak/putonghua-training.vue` 进行改造
- **编辑页新增**：创建新的编辑页面 `src/pages/speak/putonghua-edit.vue`
- **页面导航**：在训练页添加编辑按钮，支持页面间跳转

#### 2. 编辑页功能
- **字库展示**：复用现有字库组件，展示所有汉字
- **内容生成**：
  - 点击单字展示该字的所有词语、成语和对应句子
  - 无内容时显示缺省页，提供生成按钮
  - 一次性生成 15-20 个词语/成语及对应句子
- **内容管理**：
  - 分块展示：每块第一行显示词语/成语，第二行开始显示对应句子
  - 删除功能：词语/句子前添加减号图标，支持单独删除
  - 级联删除：删除词语时自动删除对应句子
- **批量操作**：支持清空当前字的所有内容，重新生成

#### 3. 训练页功能改造
- **移除实时生成**：删除现有的 API 调用生成逻辑
- **内容池展示**：从预生成的内容池中随机选择 5 个词语/成语和对应句子展示
- **字库保留**：保持现有字库展示和选择功能
- **缺省处理**：当选中字无预生成内容时，引导用户到编辑页生成内容

#### 4. 数据存储优化
- **基于现有表结构**：利用 `chatRecord` 表的 `content` 字段存储 JSON 格式的内容池数据
- **数据类型标识**：通过 `type: 'putonghuaContentPool'` 字段区分内容池数据和其他聊天记录
- **使用状态跟踪**：在 JSON 数据结构中记录内容的使用情况，避免短期内重复展示
- **数据同步**：编辑页的修改通过更新 `chatRecord` 表实时同步到训练页

### 非功能需求

#### 1. 性能要求
- 训练页内容展示响应时间 < 500ms
- 编辑页内容加载时间 < 1s
- 批量生成操作完成时间 < 10s

#### 2. 用户体验
- 页面切换流畅，无明显卡顿
- 操作反馈及时，加载状态清晰
- 界面布局合理，信息层次分明

#### 3. 数据安全
- 用户生成的内容本地存储，支持离线访问
- 数据修改操作支持撤销
- 异常情况下数据不丢失

## 技术方案

### 实现思路

#### 1. 数据结构设计

基于现有 `chatRecord` 表结构，所有内容池数据将以 JSON 格式存储在 `content` 字段中：

**chatRecord 表使用方式：**
- `title`: 存储汉字字符，如 "爱"
- `content`: 存储 JSON 序列化的 ContentPool 数据结构
- `createTime`、`updateTime`: 系统自动维护
- `_id`: 系统自动生成的唯一标识

**存储在 content 字段中的 JSON 数据结构：**
```typescript
interface ContentPool {
  type: 'putonghuaContentPool';        // 标识数据类型
  character: string;                   // 汉字
  totalItems: number;                  // 总内容数量
  lastGenerateTime: string;            // 最后生成时间
  items: ContentItem[];                // 内容项列表
}

interface ContentItem {
  id: string;                          // 唯一标识
  type: 'word' | 'idiom';             // 类型：词语或成语
  text: string;                        // 文本内容
  pinyin: string;                      // 拼音
  sentences: SentenceItem[];           // 对应句子
  usedCount: number;                   // 使用次数
  lastUsedTime: string;                // 最后使用时间
}

interface SentenceItem {
  id: string;                          // 唯一标识
  text: string;                        // 句子内容
  pinyin: string;                      // 句子拼音
  usedCount: number;                   // 使用次数
  lastUsedTime: string;                // 最后使用时间
}
```

#### 2. API 接口改造

基于现有 `chatRecord` 表结构的数据存储和读取：

```typescript
// 批量生成接口 - 生成内容并存储到 chatRecord 表
export const generateBatchContentForChar = async (
  char: string,
  count: number = 20
): Promise<ContentPool> => {
  const params = {
    message: `请为汉字"${char}"生成${count}个词语和成语`,
    model: 'deepseek-chat',
    system: `# 角色
你是一位专业的语言学家和内容创作者，擅长基于单个汉字进行丰富的语言扩展。

## 任务
1. 根据用户提供的汉字，生成指定数量的词语和成语
2. 为每个词语/成语生成 1-2 个使用句子
3. 确保内容多样性，避免重复
4. 提供标准拼音标注

## 输出格式
{
  "items": [
    {
      "type": "word",
      "text": "词语",
      "pinyin": "cí yǔ",
      "sentences": [
        {"text": "句子 1", "pinyin": "jù zi yī"},
        {"text": "句子 2", "pinyin": "jù zi èr"}
      ]
    }
  ]
}`,
    temperature: 1.2,
    top_p: 0.9
  };

  const res = await request.post('/ai/speak', params);
  const contentPool = processGeneratedContent(char, res.content);

  // 存储到 chatRecord 表
  await saveContentPoolToDatabase(char, contentPool);

  return contentPool;
};

// 从 chatRecord 表读取内容池数据
export const getContentPoolFromDatabase = async (char: string): Promise<ContentPool | null> => {
  try {
    const records = await getChatRecordListApi(`title == "${char}" && deleteTime == ""`);
    const contentRecord = records.find(record => {
      try {
        const content = JSON.parse(record.content);
        return content.type === 'putonghuaContentPool';
      } catch {
        return false;
      }
    });

    if (contentRecord) {
      return JSON.parse(contentRecord.content) as ContentPool;
    }
    return null;
  } catch (error) {
    console.error('读取内容池失败：', error);
    return null;
  }
};

// 保存内容池数据到 chatRecord 表
export const saveContentPoolToDatabase = async (char: string, contentPool: ContentPool): Promise<void> => {
  try {
    const existingRecord = await getContentPoolRecord(char);
    const recordPayload = {
      title: char,
      content: JSON.stringify(contentPool)
    };

    if (existingRecord) {
      // 更新现有记录
      await updateChatRecordApi(existingRecord._id, recordPayload);
    } else {
      // 创建新记录
      await addChatRecordApi(recordPayload);
    }
  } catch (error) {
    console.error('保存内容池失败：', error);
    throw error;
  }
};
```

#### 3. 组件架构设计

```mermaid
graph TD
    A[普通话训练模块] --> B[训练页 putonghua-training.vue]
    A --> C[编辑页 putonghua-edit.vue]
    
    B --> D[字库组件 z-word-bank]
    B --> E[内容展示组件 l-content-display]
    B --> F[音频录制组件 z-audio-recorder]
    
    C --> D
    C --> G[内容编辑组件 l-content-editor]
    C --> H[批量操作组件 l-batch-operations]
    
    I[内容池服务 ContentPoolService] --> B
    I --> C
    
    J[数据存储层 chatRecord] --> I
```

#### 4. 核心服务实现
```typescript
// 内容池管理服务
class ContentPoolService {
  // 获取字符的内容池 - 从 chatRecord 表读取
  async getContentPool(character: string): Promise<ContentPool | null> {
    return await getContentPoolFromDatabase(character);
  }
  
  // 随机选择内容用于训练
  selectRandomContent(pool: ContentPool, count: number = 5): ContentItem[] {
    const availableItems = pool.items.filter(item => 
      !this.isRecentlyUsed(item, 24 * 60 * 60 * 1000) // 24 小时内未使用
    );
    
    return this.shuffleArray(availableItems).slice(0, count);
  }
  
  // 更新使用状态 - 更新 chatRecord 表中的数据
  async updateUsageStatus(character: string, itemIds: string[]): Promise<void> {
    const pool = await this.getContentPool(character);
    if (!pool) return;

    const now = new Date().toISOString();
    pool.items.forEach(item => {
      if (itemIds.includes(item.id)) {
        item.usedCount++;
        item.lastUsedTime = now;
      }
    });

    // 更新到 chatRecord 表
    await saveContentPoolToDatabase(character, pool);
  }
  
  // 删除内容项 - 从 chatRecord 表中删除
  async removeContentItem(character: string, itemId: string): Promise<void> {
    const pool = await this.getContentPool(character);
    if (!pool) return;

    pool.items = pool.items.filter(item => item.id !== itemId);
    pool.totalItems = pool.items.length;

    // 更新到 chatRecord 表
    await saveContentPoolToDatabase(character, pool);
  }

  // 删除句子项 - 从指定词语/成语中删除句子
  async removeSentenceItem(character: string, itemId: string, sentenceId: string): Promise<void> {
    const pool = await this.getContentPool(character);
    if (!pool) return;

    const item = pool.items.find(item => item.id === itemId);
    if (item) {
      item.sentences = item.sentences.filter(sentence => sentence.id !== sentenceId);
    }

    // 更新到 chatRecord 表
    await saveContentPoolToDatabase(character, pool);
  }

  // 检查是否最近使用过
  private isRecentlyUsed(item: ContentItem, timeThreshold: number): boolean {
    if (!item.lastUsedTime) return false;
    const lastUsed = new Date(item.lastUsedTime).getTime();
    const now = Date.now();
    return (now - lastUsed) < timeThreshold;
  }

  // 数组随机排序
  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
}
```

### 技术栈与约束

#### 技术栈
- **前端框架**：Vue 3 + Composition API
- **UI 组件**：uni-app 内置组件 + 自定义组件
- **状态管理**：Vue 3 reactive API
- **数据存储**：本地数据库（基于现有 chatRecord 表，content 字段存储 JSON 数据）
- **AI 服务**：deepseek-chat 模型
- **路由管理**：uni-app 路由系统

#### 约束条件
- **兼容性**：支持微信小程序、H5、App 端
- **存储限制**：单个内容池不超过 100KB
- **API 限制**：批量生成单次不超过 30 个内容项
- **性能约束**：内容选择算法时间复杂度 O(n)

#### 开发规范
- **组件命名**：编辑页组件使用 `l-` 前缀，放在 `src/pages/speak/components/` 目录
- **文件命名**：使用 kebab-case 格式
- **代码规范**：遵循项目现有的 Vue 3 + TypeScript 规范

## 风险评估

### 假设与未知因素

1. **假设条件**：
   - AI 生成内容质量稳定，批量生成不会显著降低质量
   - 用户设备存储空间足够存储大量预生成内容
   - 用户网络环境支持一次性批量生成操作

2. **未知因素**：
   - 批量生成时 AI 模型的响应时间和稳定性
   - 不同汉字的内容生成难度差异
   - 用户对内容多样性的实际需求程度

### 潜在风险

#### 1. 技术风险
- **数据存储风险**：大量内容可能导致存储空间不足
  - **解决方案**：实现内容清理机制，定期清理长期未使用的内容
- **性能风险**：批量操作可能导致页面卡顿
  - **解决方案**：使用异步处理和进度提示，分批处理大量数据

#### 2. 用户体验风险
- **学习路径中断**：编辑页操作可能打断学习流程
  - **解决方案**：提供快速生成选项，支持在训练页直接触发内容生成
- **内容质量风险**：批量生成可能降低内容相关性
  - **解决方案**：优化提示词，增加内容质量检查机制

#### 3. 业务风险
- **迁移风险**：现有用户数据和使用习惯的兼容性
  - **解决方案**：实现数据迁移脚本，保持向后兼容
- **成本风险**：初期批量生成可能增加 API 调用成本
  - **解决方案**：实现智能生成策略，根据用户使用频率调整生成数量

### 应对策略

1. **分阶段实施**：先实现核心功能，再逐步添加高级特性
2. **A/B 测试**：对比新旧方案的用户满意度和使用效果
3. **监控机制**：实时监控系统性能和用户行为数据
4. **回滚预案**：保留现有功能作为备选方案，确保可快速回退
