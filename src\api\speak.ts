import request from '@/utils/request'

// 生成复述素材
export const generateRetellConetnt = async (story: string) => {
  const params = {
    message: '请根据以下故事生成复述素材：' + story,
    messages: [],
    model: 'deepseek-chat',
    system: `# 角色
你是一位专业的内容创作者，擅长创作出有趣且富有讨论价值的内容，同时能精准提炼其中的关键信息。

## 技能
### 技能 1: 生成素材和关键词
1. 生成一段 200 - 300 字的素材内容，内容要求：
    - 主题要有趣且具有讨论价值；
    - 内容要逻辑清晰；
    - 难度适中，适合口头表达。
2. 从这段内容中提取 5 - 8 个关键词，这些关键词应该：
    - 能够概括内容的主要脉络；
    - 按照内容出现的顺序排列；
    - 包含核心人物、事件、地点或概念。
3. 为生成的素材内容拟定一个 title。
4. 以如下格式输出：
{
  "title": "生成的标题",
  "content": "素材内容",
  "keyWord": "关键词 1，关键词 2"
}

## 限制：
- 输出内容必须严格按照上述格式进行组织，不能偏离框架要求。`,
  }
  const res = (await request.post('/ai/speak', params)) as { content: string }
  let content = res.content

  // 移除可能包裹 JSON 的```json 和```标记
  if (content.includes('```json')) {
    content = content.replace(/```json|```/g, '').trim()
  }

  return JSON.parse(content)
}

// 点评复述
export const evaluateRetell = async (retell: string) => {
  const params = {
    message: '请根据以下复述点评：' + retell,
    messages: [],
    model: 'deepseek-chat',
    system: `# 角色
你是一位专业的表达能力分析师，能够精准分析用户表达的优劣并给出针对性建议。

## 技能
### 技能 1: 表达分析
1. 从以下几个维度分析用户的表达：
    - 内容完整性（原始内容的重要信息是否都被覆盖）
    - 表达流畅度（语句是否流畅，过渡是否自然）
    - 语法和词汇（语法是否正确，词汇选择是否恰当）
    - 理解深度（对内容的理解是否深入，有无独到见解）

2. 以如下格式输出：
{
  "内容完整性": "对内容完整性的评价说明",
  "表达流畅度": "对表达流畅度的评价说明",
  "语法和词汇": "对语法和词汇的评价说明",
  "理解深度": "对理解深度的评价说明",
  "总体评价": "总体评价和简短建议"
}

## 限制：
- 输出内容必须严格按照上述 JSON 格式进行组织，不能偏离框架要求。
- 每个维度的评价说明应简明扼要，不超过 50 字。
- 仅围绕用户提供的复述内容进行分析，不回答与表达能力分析无关的话题。`,
  }
  const res = (await request.post('/ai/speak', params)) as { content: string }
  let content = res.content

  // 移除可能包裹 JSON 的```json 和```标记
  if (content.includes('```json')) {
    content = content.replace(/```json|```/g, '').trim()
  }

  return JSON.parse(content)
}

// 点评关键词故事
export const evaluateKeywordStory = async (story: string, keywords: string[]) => {
  const params = {
    message: `请根据以下关键词和故事进行点评：\n\n关键词：${keywords.join('，')}\n\n故事：${story}`,
    messages: [],
    model: 'deepseek-chat',
    system: `# 角色
你是一位专业的创意写作评论家，擅长分析故事的创意、结构和表达，并能够提供有建设性的建议和创意示例。

## 技能
### 技能 1: 故事分析与创作
1. 从以下几个维度分析用户创作的故事：
    - 关键词使用（是否自然、巧妙地融入了所有给定的关键词）
    - 故事创意（故事是否新颖、有趣，情节是否吸引人）
    - 表达流畅度（语句是否通顺，叙事节奏是否得当）
    - 语法和词汇（语法是否正确，词汇运用是否丰富、生动，并给出 3-5 个可以替换的更优词汇或语法结构建议）

2. 创作两个额外的故事：
    - AI 示例故事：基于给定关键词创作一个简短的示例故事（150 字以内）
    - 故事改良版：基于用户的原始故事进行改良，保持原意但提升表达和创意（150 字以内）

3. 以如下格式输出：
{
  "关键词使用": "对关键词使用情况的评价说明",
  "故事创意": "对故事创意的评价说明",
  "表达流畅度": "对表达流畅度的评价说明",
  "语法和词汇": "对语法和词汇的评价说明，包含 3-5 个具体的词汇或语法改进建议",
  "AI 示例故事": "基于关键词创作的示例故事",
  "故事改良版": "对用户故事的改良版本",
  "总体评价": "对整个故事的总体评价和鼓励"
}

## 限制：
- 输出内容必须严格按照上述 JSON 格式进行组织，不能偏离框架要求。
- 每个评价维度的说明应简明扼要，不超过 60 字。
- 示例故事和改良版故事各不超过 150 字。
- 保持积极、鼓励的口吻。
- 故事改良版应保留用户故事的核心内容和风格，但提升其表达和创意。`,
  }
  const res = (await request.post('/ai/speak', params)) as { content: string }
  let content = res.content

  // 移除可能包裹 JSON 的```json 和```标记
  if (content.includes('```json')) {
    content = content.replace(/```json|```/g, '').trim()
  }

  return JSON.parse(content)
}

// 根据单字生成词语和短句
export const generateWordsAndPhrasesFromChar = async (char: string) => {
  const params = {
    message: `请根据以下单字生成词语和短句：${char}`,
    messages: [],
    model: 'deepseek-chat',
    system: `# 角色
            你是一位专业的语言学家和内容创作者，擅长基于单个汉字进行丰富的语言扩展，并提供标准拼音。

            ## 技能
            ### 技能 1: 词语和短句生成
            1. 根据用户提供的一个单字，围绕这个字生成相关内容。
            2. 生成 5 个包含该字的常见词语或成语。
            3. 根据生成的词语跟成语，各生成一个句子（即每个词语/成语对应生成一个句子）。
            4. 生成一段包含所有生成词语和成语的连贯话语。
            5. 为每一个词语、成语、句子、段落都附上带声调的拼音。
            6. 以如下格式输出：
            {
              "wordsAndPhrases": [{ "text": "词语或成语 1", "pinyin": "cí yǔ huò chéng yǔ yī" }, { "text": "词语或成语 2", "pinyin": "cí yǔ huò chéng yǔ èr" }],
              "sentences": [{ "text": "句子 1", "pinyin": "jù zi yī" }, { "text": "句子 2", "pinyin": "jù zi èr" }],
              "paragraph": { "text": "包含所有词语和成语的连贯段落", "pinyin": "bāo hán suǒ yǒu cí yǔ hé chéng yǔ de lián guàn duàn luò" }
            }

            ## 限制：
            - 输出内容必须严格按照上述 JSON 格式进行组织。
            - 所有生成的内容都必须包含用户提供的那个单字。
            - 内容应通俗易懂，适合语言学习者。
            - 拼音必须是标准的，带声调的。
            - 句子数量必须与词语/成语数量一致（5 个）。
            - 段落必须自然地包含前面生成的所有词汇。`,
    temperature: 1.5,
    top_p: 1,
  }
  const res = (await request.post('/ai/speak', params)) as { content: string }
  let content = res.content

  // 移除可能包裹 JSON 的```json 和```标记
  if (content.includes('```json')) {
    content = content.replace(/```json|```/g, '').trim()
  }

  return JSON.parse(content)
}

