{
  "pages": [
    // 新增主页
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "主页",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false,
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/aiAssistant/index",
      "style": {
        "navigationBarTitleText": "ai 助手",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false,
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    // pc
    {
      "path": "pc/index",
      "style": {
        "navigationBarTitleText": "主页",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    // 日记
    {
      "path": "pages/memo/diary",
      "style": {
        "app-plus": {
          "pullToRefresh": {
            "offset": "44px",
            "style": "circle"
          }
        },
        "enablePullDownRefresh": true,
        "h5": {
          "pullToRefresh": {
            "offset": "44px",
            "support": true
          }
        },
        "navigationBarTitleText": "记"
      }
    },
    // 周目标与周复盘编辑页面
    {
      "path": "pages/okr/weekEdit",
      "style": {
        "enablePullDownRefresh": false,
        "navigationBarTitleText": "编辑",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/memo/diary_edit",
      "style": {
        "navigationBarTitleText": "记录日记",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/setting/download-data"
    },
    {
      "path": "pages/bill/index"
    },
    {
      "path": "pages/speak/vox-flow"
    },
    {
      "path": "pages/speak/note"
    },
    {
      "path": "pages/speak/putonghua-training"
    },
    {
      "path": "pages/speak/news",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "新闻资讯"
      }
    },
    {
      "path": "pages/speak/keyword-story-page",
      "style": {
        "navigationBarTitleText": "关键词讲故事",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/speak/index",
      "style": {
        "navigationBarTitleText": "表达训练"
      }
    },
    {
      "path": "pages/home/<USER>",
      "style": {
        "app-plus": {
          "pullToRefresh": {
            "offset": "44px",
            "style": "circle"
          }
        },
        "enablePullDownRefresh": true,
        "h5": {
          "pullToRefresh": {
            "offset": "44px",
            "support": true
          }
        },
        "navigationBarTitleText": "今天",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/bill/flomo"
    },
    // 记账
    {
      "path": "pages/bill/flow"
    },
    {
      "path": "pages/bill/excel"
    },
    {
      "path": "pages/bill/chart"
    },
    {
      "path": "pages/bill/enter"
    },
    {
      "path": "pages/bill/category"
    },
    {
      "path": "pages/transfer",
      "style": {
        "topWindow": false, // 当前页面不显示 topWindow
        "leftWindow": false, // 当前页面不显示 leftWindow
        "rightWindow": false, // 当前页面不显示 rightWindow
        "app-plus": {
          "pullToRefresh": {
            "offset": "44px",
            "style": "circle"
          }
        },
        "enablePullDownRefresh": true,
        "h5": {
          "pullToRefresh": {
            "offset": "44px",
            "support": true
          }
        },
        "navigationBarTitleText": "文件传输",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/okr/okrList",
      "style": {
        "app-plus": {
          "pullToRefresh": {
            "offset": "44px",
            "style": "circle"
          }
        },
        // "enablePullDownRefresh": true,
        // "h5": {
        //   "pullToRefresh": {
        //     "offset": "44px",
        //     "support": true
        //   }
        // },
        "navigationBarTitleText": "目标列表",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/home/<USER>",
      "style": {
        "app-plus": {
          "pullToRefresh": {
            "offset": "88px",
            "style": "circle"
          }
        },
        "enablePullDownRefresh": true,
        "h5": {
          "pullToRefresh": {
            "offset": "44px",
            "support": true
          }
        },
        "navigationBarTitleText": "任务统计",
        "navigationStyle": "custom"
      }
    },
    // {
    //   "path": "pages/sqlite/index",
    //   "style": {
    //     "navigationBarTitleText": "sqlite"
    //   }
    // },
    {
      "path": "pages/list/listtt",
      "style": {
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/habit/list",
      "style": {
        "enablePullDownRefresh": false,
        "navigationBarTitleText": "习惯列表"
      }
    },
    {
      "path": "pages/okr/krEdit",
      "style": {
        "enablePullDownRefresh": false,
        "navigationBarTitleText": "关键结果",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/habit/stats",
      "style": {
        "enablePullDownRefresh": false,
        "navigationBarTitleText": "统计"
      }
    },
    {
      "path": "pages/habit/edit",
      "style": {
        "enablePullDownRefresh": false,
        "navigationBarTitleText": "编辑习惯"
      }
    },
    {
      "path": "pages/habit/label",
      "style": {
        "enablePullDownRefresh": false,
        "navigationBarTitleText": "习惯标签"
      }
    },
    {
      "path": "pages/memo/memo",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "memo"
      }
    },
    {
      "path": "pages/memos/memos",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "memos"
      }
    },
    {
      "path": "pages/memos/detail",
      "style": {
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/okr/okrEdit",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "新建目标",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/todo/list",
      "style": {
        "enablePullDownRefresh": false,
        "navigationBarTitleText": "待办"
      }
    },
    {
      "path": "pages/grid/grid",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/setting/setting",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/setting/db",
      "style": {
        "navigationBarTitleText": "数据库"
      }
    },
    {
      "path": "pages/setting/account",
      "style": {
        "navigationBarTitleText": "我的账户"
      }
    },
    {
      "path": "pages/ucenter/ucenter",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/uni-agree/uni-agree",
      "style": {
        "app-plus": {
          "popGesture": "none"
        },
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/ucenter/settings/settings",
      "style": {
        "navigationBarTitleText": "设置"
      }
    },
    {
      "path": "pages/ucenter/read-news-log/read-news-log",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "阅读记录"
      }
    },
    {
      "path": "pages/ucenter/about/about",
      "style": {
        "app-plus": {
          "titleNView": {
            "buttons": [
              {
                "type": "share"
              }
            ]
          }
        },
        "navigationBarTitleText": "关于"
      }
    },
    {
      "path": "uni_modules/uni-upgrade-center-app/pages/upgrade-popup",
      "style": {
        "app-plus": {
          "animationDuration": 200,
          "animationType": "fade-in",
          "background": "transparent",
          "backgroundColorTop": "transparent",
          "popGesture": "none",
          "scrollIndicator": false,
          "titleNView": false
        },
        "disableScroll": true
      }
    },
    {
      "path": "pages/ucenter/invite/invite",
      "style": {
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/okr/okrDetail",
      "style": {
        "app-plus": {
          "pullToRefresh": {
            "offset": "88px",
            "style": "circle"
          }
        },
        "enablePullDownRefresh": true,
        "h5": {
          "pullToRefresh": {
            "offset": "44px",
            "support": true
          }
        },
        "navigationBarTitleText": "目标详情",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/okr/krDetail",
      "style": {
        "app-plus": {
          "pullToRefresh": {
            "offset": "44px",
            "style": "circle"
          }
        },
        "enablePullDownRefresh": true,
        "h5": {
          "pullToRefresh": {
            "offset": "44px",
            "support": true
          }
        },
        "navigationBarTitleText": "任务详情",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/okr/today",
      "style": {
        // "app-plus": {
        //   "pullToRefresh": {
        //     "offset": "88px",
        //     "style": "circle"
        //   }
        // },
        // "enablePullDownRefresh": false,
        // "h5": {
        //   "pullToRefresh": {
        //     "offset": "44px",
        //     "support": true
        //   }
        // },
        "navigationBarTitleText": "今日任务",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/okr/data-analysis",
      "style": {
        "enablePullDownRefresh": false,
        "navigationBarTitleText": "OKR 数据分析"
      }
    },
    {
      "path": "uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify",
      "style": {
        "enablePullDownRefresh": false,
        "navigationBarTitleText": "实名认证"
      }
    },
    {
      "path": "windows/leftWindow",
      "style": {}
    },
    {
      "path": "pages/setting/db-sync",
      "style": {
        "enablePullDownRefresh": false,
        "navigationBarTitleText": "数据同步",
        "navigationStyle": "custom"
      }
    },
    // 滴答清单相关页面
    {
      "path": "pages/dida/login",
      "style": {
        "enablePullDownRefresh": false,
        "navigationBarTitleText": "滴答清单登录",
        "navigationStyle": "custom"
      }
    }
  ],
  "leftWindow": {
    // "path": "pages/sqlite/index",
    "path": "windows/leftWindow",
    "style": {
      "width": "350px"
    },
    "matchMedia": {
      "minWidth": 768 //生效条件，当窗口宽度大于 768px 时显示
    }
  },
  "rightWindow": {},
  // "path": "windows/rightWindow",
  // "style": {
  //   "flex": 1.8
  //   // "width": "100%"
  // },
  // "matchMedia": {
  //   "minWidth": 768 //生效条件，当窗口宽度大于 768px 时显示
  // }
  "subPackages": [
    {
      "root": "uni_modules/uni-feedback",
      "pages": [
        {
          "path": "pages/opendb-feedback/opendb-feedback",
          "style": {
            "navigationBarTitleText": "意见反馈",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "uni_modules/uni-id-pages/pages",
      "pages": [
        {
          "path": "userinfo/userinfo",
          "style": {
            "navigationBarTitleText": "个人资料"
          }
        },
        {
          "path": "login/login-withoutpwd",
          "style": {
            "topWindow": false, // 当前页面不显示 topWindow
            "leftWindow": false, // 当前页面不显示 leftWindow
            "rightWindow": false // 当前页面不显示 rightWindow
          }
        },
        {
          "path": "login/login-withpwd",
          "style": {
            "topWindow": false, // 当前页面不显示 topWindow
            "leftWindow": false, // 当前页面不显示 leftWindow
            "rightWindow": false // 当前页面不显示 rightWindow
          }
        },
        {
          "path": "userinfo/deactivate/deactivate",
          "style": {
            "navigationBarTitleText": "注销账号"
          }
        },
        {
          "path": "userinfo/bind-mobile/bind-mobile",
          "style": {
            "navigationBarTitleText": "绑定手机号码"
          }
        },
        {
          "path": "login/login-smscode",
          "style": {
            "navigationBarTitleText": "手机验证码登录"
          }
        },
        {
          "path": "register/register",
          "style": {
            "navigationBarTitleText": "注册"
          }
        },
        {
          "path": "retrieve/retrieve",
          "style": {
            "navigationBarTitleText": "重置密码"
          }
        },
        {
          "path": "common/webview/webview",
          "style": {
            "enablePullDownRefresh": false,
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "userinfo/change_pwd/change_pwd",
          "style": {
            "enablePullDownRefresh": false,
            "navigationBarTitleText": "修改密码"
          }
        },
        {
          "path": "register/register-by-email",
          "style": {
            "navigationBarTitleText": "邮箱验证码注册"
          }
        },
        {
          "path": "retrieve/retrieve-by-email",
          "style": {
            "navigationBarTitleText": "通过邮箱重置密码"
          }
        },
        {
          "path": "userinfo/set-pwd/set-pwd",
          "style": {
            "enablePullDownRefresh": false,
            "navigationBarTitleText": "设置密码"
          }
        },
        // #ifdef H5
        {
          "path": "userinfo/cropImage/cropImage"
        },
        {
          "path": "register/register-admin",
          "style": {
            "enablePullDownRefresh": false,
            "navigationBarTitleText": "注册管理员账号"
          }
        }
        // #endif
      ]
    }
  ],
  "globalStyle": {
    // #ifdef H5
    "h5": {
      "titleNView": false
    },
    // #endif
    "app-plus": {
      "animationType": "zoom-fade-out",
      "animationDuration": 300
    },
    "navigationStyle": "custom",
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "uni-starter",
    "navigationBarBackgroundColor": "#FFFFFF",
    "backgroundColor": "#F8F8F8",
    "enablePullDownRefresh": false,
    "maxWidth": 1000, // 页面最大宽度
    "rpxCalcMaxDeviceWidth": 375,
    "rpxCalcBaseDeviceWidth": 375
  },
  // "rpxCalcIncludeWidth":0
  // "condition": {
  //   "current": 0,
  //   "list": [
  //     {
  //       "name": "dev", //模式名称
  //       "path": "pages/list/listtt"
  //     }
  //   ]
  // },
  "tabBar": {
    "color": "#7A7E83",
    "height": "50px",
    "selectedColor": "#64b6f7",
    "borderStyle": "black",
    "backgroundColor": "#FFFFFF",
    "list": [
      {
        "pagePath": "pages/okr/okrList",
        "iconPath": "static/tabbar/goal.png",
        "selectedIconPath": "static/tabbar/goal_active.png",
        "text": "目标"
      },
      {
        "pagePath": "pages/okr/today",
        "iconPath": "static/tabbar/task.png",
        "selectedIconPath": "static/tabbar/task_active.png",
        "text": "今天"
      },
      {
        "pagePath": "pages/okr/data-analysis",
        "iconPath": "static/tabbar/task.png",
        "selectedIconPath": "static/tabbar/task_active.png",
        "text": "分析"
      },
      {
        "pagePath": "pages/memo/diary",
        "iconPath": "static/tabbar/task.png",
        "selectedIconPath": "static/tabbar/task_active.png",
        "text": "日记"
      },
      {
        "pagePath": "pages/setting/setting",
        "iconPath": "static/tabbar/setting.png",
        "selectedIconPath": "static/tabbar/setting_active.png",
        "text": "设置"
      }
    ]
  },
  "uniIdRouter": {
    // "loginPage": "uni_modules/uni-id-pages/pages/login/login-withoutpwd",
    "loginPage": "uni_modules/uni-id-pages/pages/login/login-withpwd",
    "needLogin": ["/uni_modules/uni-id-pages/pages/userinfo/userinfo", "pages/setting/account"],
    "resToLogin": false
  },
  "easycom": {
    "autoscan": true,
    "custom": {
      "^l-(.*)": "./components/l-$1.vue" // 匹配 components 目录内的 vue 文件
    }
  }
}
